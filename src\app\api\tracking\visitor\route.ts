import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import { UAParser } from 'ua-parser-js';
import { v4 as uuidv4 } from 'uuid';
import { generateNickname } from '@/lib/nickname-generator';
import { getLocationFromIp } from '@/lib/geo-utils';
import { createHash } from 'crypto';

/**
 * POST /api/tracking/visitor
 * Track anonymous visitor data
 *
 * Simplified tracking approach:
 * 1. Use visitor ID cookie as primary identifier
 * 2. Simple fingerprint for basic deduplication
 * 3. Create or update visitor records efficiently
 */
export async function POST(request: NextRequest) {
  try {
    // Get request data
    const data = await request.json();
    const { referrer, browserInfo: clientBrowserInfo = {} } = data;

    // Get visitor ID from cookie or create a new one
    let visitorId = request.cookies.get('visitorId')?.value;
    let isNewCookie = false;

    // If no visitor ID, create a new one
    if (!visitorId) {
      visitorId = uuidv4();
      isNewCookie = true;
    }

    // Get IP address and user agent
    const ipAddress = request.headers.get('x-forwarded-for')?.split(',')[0] ||
                      request.headers.get('x-real-ip') ||
                      'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Parse user agent to get device, browser, and OS info
    const parser = new UAParser(userAgent);
    const browserInfo = parser.getBrowser();
    const osInfo = parser.getOS();
    const deviceInfo = parser.getDevice();

    // Determine device type
    let deviceType = 'Desktop';
    if (deviceInfo.type === 'mobile' || deviceInfo.type === 'tablet') {
      deviceType = deviceInfo.type === 'mobile' ? 'Mobile' : 'Tablet';
    }

    // Generate a simple fingerprint for basic deduplication
    const fingerprint = createSimpleFingerprint({
      ipAddress,
      userAgent,
      screenWidth: clientBrowserInfo.screenWidth,
      screenHeight: clientBrowserInfo.screenHeight,
      timezone: clientBrowserInfo.timezone
    });

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Import the AnonymousVisitor model
    const AnonymousVisitor = (await import('@/models/AnonymousVisitor')).default;

    // Check if a visitor with this fingerprint already exists
    const existingVisitor = await AnonymousVisitor.findOne({
      $or: [
        { fingerprint: fingerprint },
        { visitorId: visitorId || '' }
      ]
    });

    if (existingVisitor && existingVisitor.visitorId !== visitorId) {
      // Update the visitor ID to the fingerprint match
      visitorId = existingVisitor.visitorId || visitorId || '';
    }

    let isNewVisitor = false;

    if (existingVisitor) {
      // Update existing visitor with current visit information
      await AnonymousVisitor.updateOne(
        { visitorId },
        {
          $set: {
            lastVisit: new Date(),
            fingerprint: fingerprint || existingVisitor.fingerprint,
            userAgent: userAgent || existingVisitor.userAgent,
            browser: browserInfo.name || existingVisitor.browser,
            os: osInfo.name || existingVisitor.os,
            device: deviceType || existingVisitor.device
          },
          $inc: { visitCount: 1 }
        }
      );


    } else {
      // Create a new visitor
      // Generate a friendly nickname for the visitor
      const nickname = generateNickname(visitorId || '');

      // Get location information from IP address
      const locationInfo = await getLocationFromIp(ipAddress);

      // Create new visitor with essential information
      const newVisitor = new AnonymousVisitor({
        visitorId: visitorId || '',
        nickname,
        ipAddress,
        userAgent,
        fingerprint,
        firstVisit: new Date(),
        lastVisit: new Date(),
        visitCount: 1,
        pagesViewed: 0,
        referrer: referrer || 'Direct',
        browser: browserInfo.name,
        os: osInfo.name,
        device: deviceType,
        country: locationInfo.country,
        countryCode: locationInfo.countryCode,
        region: locationInfo.region,
        city: locationInfo.city,
        timezone: locationInfo.timezone,
        latitude: locationInfo.latitude,
        longitude: locationInfo.longitude,
        convertedToUser: false,
        metadata: {
          // Store only essential identification details
          screenWidth: clientBrowserInfo.screenWidth,
          screenHeight: clientBrowserInfo.screenHeight,
          timezone: clientBrowserInfo.timezone,
          language: clientBrowserInfo.language,
          creationDate: new Date()
        }
      });

      // Save new visitor with duplicate key error handling
      try {
        await newVisitor.save();
        isNewVisitor = true;
      } catch (saveError: unknown) {
        // Handle duplicate key error (E11000)
        const isDuplicateKeyError = saveError &&
          typeof saveError === 'object' &&
          'code' in saveError &&
          (saveError as { code: number }).code === 11000;

        if (isDuplicateKeyError) {
          // Update existing visitor instead
          await AnonymousVisitor.updateOne(
            { visitorId: visitorId || '' },
            {
              $set: {
                lastVisit: new Date(),
                fingerprint: fingerprint || undefined,
                userAgent: userAgent || undefined,
                browser: browserInfo.name || undefined,
                os: osInfo.name || undefined,
                device: deviceType || undefined
              },
              $inc: { visitCount: 1 }
            }
          );
          isNewVisitor = false;
        } else {
          throw saveError;
        }
      }


    }

    // Create the response
    const response = NextResponse.json({
      success: true,
      isNewVisitor,
      visitorId
    });

    // Set cookie if it's a new visitor
    if (isNewCookie && visitorId) {
      // Set cookie with visitor ID (expires in 1 year)
      const oneYear = 60 * 60 * 24 * 365;
      response.cookies.set('visitorId', visitorId, {
        maxAge: oneYear,
        path: '/',
        sameSite: 'lax',
        secure: process.env.NODE_ENV === 'production',
        httpOnly: true
      });
    }

    return response;
  } catch (error) {
    console.error('Error tracking visitor:', error);
    return NextResponse.json(
      { error: 'Failed to track visitor', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/tracking/visitor
 * Get current visitor ID (for client-side use)
 */
export async function GET(request: NextRequest) {
  try {
    // Get visitor ID from cookie
    const visitorId = request.cookies.get('visitorId')?.value;

    return NextResponse.json({
      visitorId: visitorId || null
    });
  } catch (error) {
    console.error('Error getting visitor ID:', error);
    return NextResponse.json(
      { error: 'Failed to get visitor ID', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * Helper function to create a simple fingerprint hash for a visitor
 * This helps identify the same visitor even if they clear cookies
 */
interface SimpleFingerprintData {
  ipAddress: string;
  userAgent: string;
  screenWidth?: number;
  screenHeight?: number;
  timezone?: string;
}

function createSimpleFingerprint(data: SimpleFingerprintData): string {
  try {
    // Create a simple fingerprint using only essential data
    const fingerprintString = [
      data.ipAddress,
      data.userAgent,
      data.screenWidth,
      data.screenHeight,
      data.timezone
    ].filter(Boolean).join('|');

    // If we don't have enough data, return empty
    if (fingerprintString.length < 10) {
      return '';
    }

    // Create a SHA-256 hash
    return createHash('sha256')
      .update(fingerprintString)
      .digest('hex');
  } catch (error) {
    console.error('Error creating visitor fingerprint:', error);
    return '';
  }
}
